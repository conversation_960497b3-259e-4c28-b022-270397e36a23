# GT Plus Flutter Project Setup Guide

## Project Overview
GT Plus is a Flutter-based health assessment application that integrates with various medical devices and provides comprehensive health screening capabilities including audiometry, blood pressure monitoring, temperature measurement, and questionnaire-based assessments.

## Prerequisites

### 1. System Requirements
- **Flutter SDK**: 3.29.2 (Channel stable)
- **Dart SDK**: 3.7.2 (included with Flutter)
- **Xcode**: 16.1 or later
- **Android Studio**: 2025.1 or later
- **CocoaPods**: 1.16.2 or later

### 2. Development Tools
- **VS Code** with Flutter extension (recommended)
- **Android Studio** with Flutter and Dart plugins
- **Git** for version control

## Installation Steps

### 1. Flutter SDK Setup
```bash
# Download Flutter SDK from https://flutter.dev/docs/get-started/install/macos
# Extract to desired location (e.g., ~/flutter)
export PATH="$PATH:[PATH_TO_FLUTTER_GIT_DIRECTORY]/flutter/bin"

# Verify installation
flutter doctor -v
```

### 2. Android Development Setup
```bash
# Install Android Studio from https://developer.android.com/studio
# During installation, ensure Android SDK is installed

# Set environment variables in ~/.zshrc or ~/.bash_profile
export ANDROID_HOME=$HOME/Library/Android/sdk
export PATH=$PATH:$ANDROID_HOME/emulator
export PATH=$PATH:$ANDROID_HOME/tools
export PATH=$PATH:$ANDROID_HOME/tools/bin
export PATH=$PATH:$ANDROID_HOME/platform-tools

# Accept Android licenses
flutter doctor --android-licenses
```

### 3. iOS Development Setup
```bash
# Install Xcode from Mac App Store
# Install Xcode command line tools
sudo xcode-select --install

# Install CocoaPods
sudo gem install cocoapods
```

### 4. Project Setup
```bash
# Clone the repository
git clone [REPOSITORY_URL]
cd Gt_Plus_flutter

# Install Flutter dependencies
flutter pub get

# For iOS: Install CocoaPods dependencies
cd ios
pod install
cd ..
```

## Configuration

### 1. Firebase Configuration
The project uses Firebase for analytics, crashlytics, and remote configuration.

**Required Files:**
- `android/app/google-services.json` ✅ (Already present)
- `ios/Runner/GoogleService-Info.plist` ✅ (Already present)

**Firebase Project Details:**
- Project ID: `sfoto-clinic-396605`
- Android Package: `com.saiwell.gt`
- iOS Bundle ID: `com.saiwell.gt`

### 2. Environment Configuration
The app supports two environments:
- **Development/Staging**: Default environment
- **Production**: Set via `ENVIRONMENT=prod` build flag

**API Endpoints:**
- **Staging**: `https://sh-cron-trigger-staging-391397853270.us-west1.run.app/`
- **Production**: `https://sh-cron-trigger-391397853270.us-west1.run.app/`

### 3. Google Cloud Configuration
- Google Cloud credentials are located at `assets/gCloud/credentials.json`
- Used for Google APIs integration

### 4. Remote Configuration Keys
The app uses Firebase Remote Config with these keys:
- `TIMER_IN_SECONDS_GT_DEV` / `TIMER_IN_SECONDS_GT_PROD`
- `INSTRUCTIONS_GT`
- `IS_FLASH_NEEDED_GT`
- `AUDIOMETRY_PROMPT_GT`
- `COGNIVUE_PROMPT_GT`
- `MIN_VERSION_GT_DEV` / `MIN_VERSION_GT_PROD`
- `QUESTIONNAIRES_STAGING` / `QUESTIONNAIRES_PRODUCTION`

## Building and Running

### 1. Development Build
```bash
# Run on iOS simulator
flutter run -d ios

# Run on Android emulator
flutter run -d android

# Run on connected device
flutter devices
flutter run -d [DEVICE_ID]
```

### 2. Production Build
```bash
# Build for iOS (staging)
flutter build ios --release

# Build for iOS (production)
flutter build ios --release --dart-define=ENVIRONMENT=prod

# Build for Android (staging)
flutter build apk --release

# Build for Android (production)
flutter build apk --release --dart-define=ENVIRONMENT=prod
```

## Key Features & Dependencies

### 1. Core Functionality
- **Health Assessments**: Audiometry, blood pressure, temperature monitoring
- **Bluetooth Integration**: Device connectivity via `flutter_blue_plus`
- **Camera Integration**: Image capture and processing
- **Questionnaires**: Dynamic questionnaire system
- **Data Sync**: Cloud-based data synchronization

### 2. Major Dependencies
- **State Management**: GetX (`get: ^4.7.2`)
- **Firebase**: Analytics, Crashlytics, Remote Config
- **Bluetooth**: `flutter_blue_plus: ^1.35.3`
- **Camera**: `camera: ^0.11.0+2`
- **Charts**: `syncfusion_flutter_charts: ^28.2.12`
- **Image Processing**: `image: ^4.5.3`, `image_cropper: ^9.0.0`
- **Permissions**: `permission_handler: ^11.4.0`

## Platform-Specific Notes

### iOS
- **Minimum iOS Version**: 15.5
- **Required Permissions**: Camera, Bluetooth, Location
- **Custom Frameworks**: LSBluetoothPlugin.xcframework for specialized Bluetooth functionality
- **ARKit Support**: Enabled for 3D scanning capabilities

### Android
- **Minimum SDK**: As defined in Flutter configuration
- **Target SDK**: Latest available
- **Package Name**: `com.saiwell.gt`
- **Required Permissions**: Camera, Bluetooth, Location, Storage

## Troubleshooting

### 1. Common Issues
```bash
# Clear Flutter cache
flutter clean
flutter pub get

# Reset iOS pods
cd ios
rm -rf Pods Podfile.lock
pod install
cd ..

# Reset Android build
cd android
./gradlew clean
cd ..
```

### 2. Firebase Issues
- Ensure `google-services.json` and `GoogleService-Info.plist` are properly configured
- Verify Firebase project settings match the configuration files

### 3. Bluetooth Issues
- Ensure proper permissions are granted
- Check device compatibility for Bluetooth features

## Development Workflow

### 1. Code Structure
- `lib/`: Main application code
- `assets/`: Static assets (images, fonts, JSON data)
- `android/`: Android-specific configuration
- `ios/`: iOS-specific configuration

### 2. Testing
```bash
# Run unit tests
flutter test

# Run integration tests
flutter drive --target=test_driver/app.dart
```

### 3. Debugging
- Use Flutter Inspector in VS Code/Android Studio
- Enable Firebase Crashlytics for production crash reporting
- Use `flutter logs` for real-time debugging

## Deployment

### 1. iOS App Store
- Build archive in Xcode
- Upload via Xcode or Application Loader
- Configure app metadata in App Store Connect

### 2. Google Play Store
- Build signed APK/AAB
- Upload via Google Play Console
- Configure store listing and metadata

## Support & Maintenance

### 1. Monitoring
- Firebase Analytics for user behavior
- Firebase Crashlytics for crash reporting
- Remote Config for feature flags

### 2. Updates
- Use Firebase Remote Config for dynamic updates
- Follow semantic versioning for app releases
- Test thoroughly on both platforms before release

## Additional Setup Requirements

### 1. Asset Files
The project includes numerous JSON configuration files in `assets/json/`:
- Medical condition data (Diagnosis.json, Drugs.json, etc.)
- Demographic data (States.json, Towns.json, etc.)
- Questionnaire configurations (QuestionnaireData.json)
- Ensure all asset files are present before building

### 2. Font Assets
Custom Arial font family is configured:
- `assets/fonts/arial.ttf` (weight: 400)
- `assets/fonts/G_ari_bd.TTF` (weight: 700)
- `assets/fonts/ARIBL0.ttf` (weight: 900)

### 3. Scripts and Utilities
The project includes helper scripts:
- `upload_dsyms.sh`: Firebase Crashlytics dSYM upload
- `check_progress.sh`: Monitor upload progress
- `track_progress.sh`: Progress tracking utility

### 4. Local Configuration
Update `android/local.properties` with your local paths:
```properties
sdk.dir=/Users/<USER>/Library/Android/sdk
flutter.sdk=/Users/<USER>/flutter
flutter.buildMode=debug
flutter.versionName=2.3.2
flutter.versionCode=2
```

### 5. Version Information
- **Current Version**: 2.3.2+2
- **Flutter SDK Constraint**: ^3.5.4
- **Package Name**: gt_plus

## Security Considerations

### 1. API Keys and Credentials
- Firebase configuration files contain sensitive API keys
- Google Cloud credentials in `assets/gCloud/credentials.json`
- Ensure proper access controls in production

### 2. Network Security
- All API communications use HTTPS
- Staging and production environments are separated
- Implement proper authentication for API endpoints

### 3. Data Privacy
- Health data requires HIPAA compliance considerations
- Implement proper data encryption for sensitive information
- Follow platform-specific privacy guidelines

## Team Handover Checklist

### For New Developers:
- [ ] Install Flutter SDK 3.29.2
- [ ] Install Xcode 16.1+ and Android Studio 2025.1+
- [ ] Clone repository and run `flutter pub get`
- [ ] Install iOS dependencies with `pod install`
- [ ] Verify `flutter doctor` shows no critical issues
- [ ] Test build on both iOS and Android
- [ ] Verify Firebase configuration is working
- [ ] Test Bluetooth functionality with actual devices
- [ ] Review API endpoints and environment configuration
- [ ] Understand the questionnaire system and remote config
- [ ] Test camera and image processing features

### Access Requirements:
- [ ] Firebase project access (sfoto-clinic-396605)
- [ ] Google Cloud project access
- [ ] API endpoint access credentials
- [ ] Apple Developer account (for iOS deployment)
- [ ] Google Play Console access (for Android deployment)
- [ ] Repository access with appropriate permissions

---

**Important**: This is a medical application handling sensitive health data. Ensure compliance with relevant healthcare regulations (HIPAA, GDPR, etc.) and follow secure development practices throughout the development lifecycle.
