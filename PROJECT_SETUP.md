# GT Plus iPad App - Setup Guide

## What is GT Plus?
A health assessment iPad app that connects to medical devices for audiometry, blood pressure, temperature monitoring, and health questionnaires.

## Quick Setup

### 1. Requirements
- **macOS** 15.5+
- **Xcode** 16.1+
- **Flutter** 3.29.2
- **iPad** with iPadOS 15.5+

### 2. Install Tools
```bash
# Install Flutter (download from flutter.dev)
export PATH="$PATH:/path/to/flutter/bin"

# Install Xcode from Mac App Store
sudo xcode-select --install
sudo gem install cocoapods

# Verify setup
flutter doctor -v
```

### 3. Setup Project
```bash
# Clone and setup
git clone [REPOSITORY_URL]
cd Gt_Plus_flutter
flutter pub get
cd ios && pod install && cd ..
```

## Running the App

### Development
```bash
# Run on iPad simulator
flutter run -d ios

# Run on connected iPad
flutter run -d [IPAD_DEVICE_ID]
```

## Key Configuration

### Firebase (Already Configured)
- Project: `sfoto-clinic-396605`
- Bundle ID: `com.saiwell.gt`
- Config file: `ios/Runner/GoogleService-Info.plist` ✅

### Environments
- **Staging** (default): `https://sh-cron-trigger-staging-391397853270.us-west1.run.app/`
- **Production**: Add `--dart-define=ENVIRONMENT=prod` to build commands

### Important Files
- `assets/gCloud/credentials.json` - Google Cloud credentials
- `assets/json/` - Medical data and questionnaires
- `assets/fonts/` - Custom Arial fonts

## Core Features
- **Health Tests**: Audiometry, BP, temperature
- **Bluetooth**: Medical device connectivity
- **Camera**: Image capture and processing
- **ARKit**: 3D scanning capabilities
- **Questionnaires**: Dynamic health assessments

## Troubleshooting

### Common Fixes
```bash
# Clean everything
flutter clean && flutter pub get
cd ios && rm -rf Pods Podfile.lock && pod install && cd ..

# In Xcode: Product > Clean Build Folder
```

### Issues
- **Firebase not working**: Check `GoogleService-Info.plist` is present
- **Bluetooth issues**: Verify permissions in iPad settings
- **Build fails**: Run `flutter doctor` and fix any issues

## Deployment

### App Store Steps
1. Build: `flutter build ios --release --dart-define=ENVIRONMENT=prod`
2. Open `ios/Runner.xcworkspace` in Xcode
3. Create archive and upload to App Store Connect
4. Configure metadata and submit for review


### New Developer Setup
- [ ] Install Flutter 3.29.2 and Xcode 16.1+
- [ ] Clone repo and run `flutter pub get`
- [ ] Run `cd ios && pod install`
- [ ] Test on iPad simulator: `flutter run -d ios`
- [ ] Test on physical iPad
- [ ] Verify Firebase and Bluetooth work

### Access Needed
- [ ] Firebase project access (`sfoto-clinic-396605`)
- [ ] Apple Developer account
- [ ] Repository access
- [ ] API credentials

---
