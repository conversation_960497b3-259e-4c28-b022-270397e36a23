# GT Plus - Quick Reference Guide

## Common Commands

### Project Setup
```bash
# Initial setup
flutter pub get
cd ios && pod install && cd ..

# Clean and rebuild
flutter clean
flutter pub get
cd ios && rm -rf Pods Podfile.lock && pod install && cd ..
```

### Development
```bash
# Run in debug mode
flutter run

# Run with specific environment
flutter run --dart-define=ENVIRONMENT=prod

# Hot reload (during development)
# Press 'r' in terminal or use IDE hot reload

# Hot restart
# Press 'R' in terminal or use IDE hot restart
```

### Building
```bash
# Debug builds
flutter build ios --debug
flutter build apk --debug

# Release builds
flutter build ios --release
flutter build apk --release

# Production builds
flutter build ios --release --dart-define=ENVIRONMENT=prod
flutter build apk --release --dart-define=ENVIRONMENT=prod
```

### Testing
```bash
# Run all tests
flutter test

# Run specific test file
flutter test test/widget_test.dart

# Run tests with coverage
flutter test --coverage
```

## Key File Locations

### Configuration Files
- `pubspec.yaml` - Dependencies and project configuration
- `firebase.json` - Firebase project configuration
- `android/app/google-services.json` - Android Firebase config
- `ios/Runner/GoogleService-Info.plist` - iOS Firebase config
- `lib/firebase_options.dart` - Generated Firebase options

### Important Directories
- `lib/` - Main application source code
- `lib/models/` - Data models
- `lib/services/` - API and business logic services
- `lib/controllers/` - GetX controllers for state management
- `lib/modules/` - Feature modules
- `lib/utils/` - Utility classes and constants
- `assets/` - Static assets (images, fonts, JSON data)

### Platform-Specific
- `android/app/build.gradle` - Android build configuration
- `ios/Podfile` - iOS dependencies
- `ios/Runner.xcodeproj/` - iOS project configuration

## Environment Variables

### Build-time Variables
```bash
# Set environment to production
--dart-define=ENVIRONMENT=prod

# Default is development/staging
# No flag needed for dev environment
```

### API Endpoints
- **Staging**: `https://sh-cron-trigger-staging-391397853270.us-west1.run.app/`
- **Production**: `https://sh-cron-trigger-391397853270.us-west1.run.app/`

## Firebase Remote Config Keys

### Timer Configuration
- `TIMER_IN_SECONDS_GT_DEV` - Timer for development
- `TIMER_IN_SECONDS_GT_PROD` - Timer for production

### Feature Flags
- `IS_FLASH_NEEDED_GT` - Camera flash requirement
- `MIN_VERSION_GT_DEV` - Minimum app version for dev
- `MIN_VERSION_GT_PROD` - Minimum app version for prod

### Content Configuration
- `INSTRUCTIONS_GT` - App instructions
- `AUDIOMETRY_PROMPT_GT` - Audiometry test prompts
- `COGNIVUE_PROMPT_GT` - Cognitive test prompts
- `QUESTIONNAIRES_STAGING` - Staging questionnaire data
- `QUESTIONNAIRES_PRODUCTION` - Production questionnaire data

## Common Issues & Solutions

### Flutter Doctor Issues
```bash
# Android licenses not accepted
flutter doctor --android-licenses

# iOS deployment issues
sudo xcode-select --install
sudo gem install cocoapods

# Flutter version issues
flutter channel stable
flutter upgrade
```

### Build Issues
```bash
# iOS build fails
cd ios
rm -rf Pods Podfile.lock
pod install
cd ..
flutter clean
flutter pub get

# Android build fails
cd android
./gradlew clean
cd ..
flutter clean
flutter pub get

# Dependency conflicts
flutter pub deps
flutter pub upgrade
```

### Firebase Issues
```bash
# Firebase not initialized
# Check google-services.json and GoogleService-Info.plist
# Verify Firebase.initializeApp() is called in main.dart

# Remote config not working
# Check network connectivity
# Verify Firebase project configuration
# Check remote config keys in Firebase console
```

### Bluetooth Issues
```bash
# Permissions not granted
# Check Info.plist for iOS permissions
# Check AndroidManifest.xml for Android permissions
# Request permissions at runtime using permission_handler

# Device not connecting
# Ensure device is in pairing mode
# Check Bluetooth is enabled on device
# Verify device compatibility
```

## Development Workflow

### 1. Feature Development
1. Create feature branch from main
2. Implement feature in appropriate module
3. Add/update tests
4. Test on both iOS and Android
5. Create pull request

### 2. Testing Checklist
- [ ] Unit tests pass
- [ ] Widget tests pass
- [ ] Manual testing on iOS
- [ ] Manual testing on Android
- [ ] Bluetooth functionality tested
- [ ] Camera functionality tested
- [ ] API integration tested
- [ ] Firebase features tested

### 3. Release Process
1. Update version in `pubspec.yaml`
2. Update Firebase Remote Config if needed
3. Build and test release versions
4. Upload to respective app stores
5. Monitor crash reports and analytics

## Debugging Tips

### Flutter Inspector
- Use Flutter Inspector in VS Code/Android Studio
- Inspect widget tree and properties
- Monitor performance and memory usage

### Logging
```dart
import 'package:flutter/foundation.dart';

// Debug logging
debugPrint('Debug message');

// Conditional logging
if (kDebugMode) {
  print('Development only message');
}
```

### Firebase Debugging
- Check Firebase console for real-time data
- Use Firebase Analytics debug view
- Monitor Crashlytics for crash reports

### Network Debugging
- Use network inspector in browser dev tools
- Check API responses in debug mode
- Verify SSL certificates for HTTPS endpoints

## Performance Optimization

### Build Optimization
```bash
# Analyze bundle size
flutter build apk --analyze-size

# Build with tree shaking
flutter build apk --release --tree-shake-icons
```

### Code Optimization
- Use `const` constructors where possible
- Implement proper state management with GetX
- Optimize image assets and use appropriate formats
- Implement lazy loading for large lists

---

**Quick Help**: For immediate assistance, check `flutter doctor -v` for system status and refer to the main PROJECT_SETUP.md for detailed configuration instructions.
